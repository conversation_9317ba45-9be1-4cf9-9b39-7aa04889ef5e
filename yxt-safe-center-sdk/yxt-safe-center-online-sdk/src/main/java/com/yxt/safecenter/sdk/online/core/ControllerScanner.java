package com.yxt.safecenter.sdk.online.core;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.sdk.online.api.SafeInterfaceApi;
import com.yxt.safecenter.sdk.online.dto.request.SafeInterfaceOnlineReq;
import com.yxt.safecenter.sdk.online.enums.ReqMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.models.Swagger;
import io.swagger.parser.OpenAPIParser;
import io.swagger.v3.parser.core.models.SwaggerParseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.webmvc.api.OpenApiResource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.UriComponents;
import springfox.documentation.service.Documentation;
import springfox.documentation.spring.web.DocumentationCache;
import springfox.documentation.spring.web.json.JsonSerializer;
import springfox.documentation.swagger.common.HostNameProvider;
import springfox.documentation.swagger2.mappers.ServiceModelToSwagger2Mapper;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class ControllerScanner implements CommandLineRunner {

    private final RequestMappingHandlerMapping requestMappingHandlerMapping;
    private final SafeInterfaceApi safeInterfaceApi;
    private final Environment environment;
    private final ApplicationContext applicationContext;
    private Set<String> projectPackagePres = Sets.newHashSet("cn.hydee", "com.hydee");
    private final DocumentationCache documentationCache;
    private final ServiceModelToSwagger2Mapper mapper;
    private ObjectMapper objectMapper = new ObjectMapper();
    private final OpenApiResource openApiResource;



    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Override
    public void run(String... args) throws Exception {
//        Documentation aDefault = documentationCache.documentationByGroup("default");
//        Swagger swagger = this.mapper.mapDocumentation(aDefault);
//        swagger.basePath(Strings.isNullOrEmpty("localhost:8080") ? "/" : "localhost:8080");
//        if (Strings.isNullOrEmpty(swagger.getHost())) {
//            swagger.host("localhost:8080");
//        }
////        SwaggerParseResult swaggerParseResult = new OpenAPIParser().readLocation("http://localhost:8080/v2/api-docs", null, null);
//        SwaggerParseResult swaggerParseResult1 = new OpenAPIParser().readContents(JSON.toJSONString(aDefault), null, null);
        String[] activeProfiles = environment.getActiveProfiles();
        String applicationName = environment.getProperty("spring.application.name");
        String property = environment.getProperty("biz.safecenter.online.open");
        String activeProfile = activeProfiles.length == 0 ? "default" : StringUtils.join(activeProfiles, ",");
        log.info("-------------applicationName: " + applicationName);
        log.info("-------------activeProfile: " + activeProfile);
        log.info("--------------------------");
        if ("local".equals(activeProfile) || (property != null && property.equals(Boolean.FALSE.toString()))) {
            return;
        }
        initProjectPackagePres();

        // 获取所有的请求映射信息
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();

        List<SafeInterfaceOnlineReq> serverReqList = new ArrayList<>();
        Set<String> needSkipControllerSet = new HashSet<>();
        // 遍历所有的请求映射
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            RequestMappingInfo mappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();

            // 获取控制器类的包名
            String packageName = handlerMethod.getBeanType().getPackage().getName();

            // 过滤接口
            if (invalidPackage(handlerMethod, packageName)) {
                needSkipControllerSet.add(handlerMethod.getBeanType().getSimpleName());
                continue;
            }

            // 获取请求路径
            List<String> apiPath = new ArrayList<>(mappingInfo.getPatternsCondition().getPatterns());

            // 获取HTTP方法类型
            List<String> reqMethods = new ArrayList<>();
            if (CollectionUtils.isEmpty(mappingInfo.getMethodsCondition().getMethods())) {
                reqMethods = Arrays.stream(ReqMethod.values()).map(ReqMethod::name).collect(Collectors.toList());
            } else {
                for (RequestMethod httpMethod : mappingInfo.getMethodsCondition().getMethods()) {
                    reqMethods.add(httpMethod.name());
                }
            }

            // 获取控制器方法的类名和方法名
            String className = handlerMethod.getBeanType().getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            Method method = handlerMethod.getMethod();

            // 解析请求参数信息
            String reqInfo = parseRequestInfo(method);
            // 解析响应参数信息
            String respInfo = parseResponseInfo(method);
            // 解析接口名称信息
            String apiName = parseApiName(method);

            SafeInterfaceOnlineReq safeInterfaceOnlineReq = new SafeInterfaceOnlineReq();
            safeInterfaceOnlineReq.setApplicationName(applicationName);
            safeInterfaceOnlineReq.setApiClass(className);
            safeInterfaceOnlineReq.setApiMethod(methodName);
//            safeInterfaceOnlineReq.setNeibu(isFeignClient(handlerMethod)); // 是否是内部接口 todo:gx
            safeInterfaceOnlineReq.setApiWay(JsonUtils.toJson(reqMethods));
            safeInterfaceOnlineReq.setApiPath(JsonUtils.toJson(apiPath));
            safeInterfaceOnlineReq.setReqParamsInfo(reqInfo);
            safeInterfaceOnlineReq.setRespParamsInfo(respInfo);
            safeInterfaceOnlineReq.setApiName(apiName);
            serverReqList.add(safeInterfaceOnlineReq);
        }
        log.info("需要过滤的非本项目接口类：{}", JsonUtils.toJson(needSkipControllerSet));

        // 如果检测到条件不符合要求，则提前终止
        try {
            log.info("------------------------------------数据list,{}", JsonUtils.toJson(serverReqList));
            safeInterfaceApi.online(serverReqList);
        } catch (Exception e) {
            log.error("------------------------------------接口上报异常:终止应用！", e);
            System.exit(1);
        }
    }

    private boolean invalidPackage(HandlerMethod handlerMethod, String packageName) {
        boolean skip = true;
        for (String packagePre : projectPackagePres) {
            if (packageName.startsWith(packagePre)) {
                skip = false;
                break; // 如果不在指定的包内，跳过当前控制器
            }
        }
        return skip;
    }

    public boolean isFeignClient(HandlerMethod handlerMethod) {
        // 获取控制器类的 Class 对象
        Class<?> controllerClass = handlerMethod.getBeanType();
        Annotation[] annotations = controllerClass.getAnnotations();
        for (Annotation annotation : annotations) {
            if (annotation instanceof FeignClient) {
                return true;
            }
        }
        // 获取控制器类实现的所有接口
        Class<?>[] interfaces = controllerClass.getInterfaces();

        for (Class<?> iface : interfaces) {
            if (iface.getAnnotation(FeignClient.class) != null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 解析请求信息
     */
    private String parseRequestInfo(Method method) {
        try {
            Map<String, Object> requestInfo = new HashMap<>();

            Parameter[] parameters = method.getParameters();
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);

            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                String paramName = parameterNames != null && i < parameterNames.length ? parameterNames[i] : "param" + i;

                // 根据注解类型分类参数
                if (parameter.isAnnotationPresent(RequestHeader.class)) {
                    RequestHeader requestHeader = parameter.getAnnotation(RequestHeader.class);
                    String headerName = StringUtils.isNotBlank(requestHeader.value()) ? requestHeader.value() :
                            StringUtils.isNotBlank(requestHeader.name()) ? requestHeader.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "header");
                    requestInfo.put(headerName, paramSchema);
                } else if (parameter.isAnnotationPresent(PathVariable.class)) {
                    PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
                    String pathName = StringUtils.isNotBlank(pathVariable.value()) ? pathVariable.value() :
                            StringUtils.isNotBlank(pathVariable.name()) ? pathVariable.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "path");
                    requestInfo.put(pathName, paramSchema);
                } else if (parameter.isAnnotationPresent(RequestParam.class)) {
                    RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
                    String queryName = StringUtils.isNotBlank(requestParam.value()) ? requestParam.value() :
                            StringUtils.isNotBlank(requestParam.name()) ? requestParam.name() : paramName;
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "query");
                    requestInfo.put(queryName, paramSchema);
                } else if (parameter.isAnnotationPresent(RequestBody.class)) {
                    // 对于RequestBody，创建对象schema，使用变量名作为key
                    Map<String, Object> bodySchema = createTypeSchema(parameter.getType(), parameter.getParameterizedType());
                    bodySchema.put("in", "body");
                    requestInfo.put(paramName, bodySchema);
                } else {
                    // 没有注解的参数默认作为RequestParam处理
                    Map<String, Object> paramSchema = createParameterSchema(parameter, "query");
                    requestInfo.put(paramName, paramSchema);
                }
            }

            return JsonUtils.toJson(requestInfo);
        } catch (Exception e) {
            log.error("解析请求信息失败", e);
            return "{}";
        }
    }

    /**
     * 解析响应信息
     */
    private String parseResponseInfo(Method method) {
        try {
            Map<String, Object> responseInfo = new HashMap<>();

            // 获取返回类型
            Type returnType = method.getGenericReturnType();
            Class<?> returnClass = method.getReturnType();

            // 创建响应schema
            Map<String, Object> respSchema = createTypeSchema(returnClass, returnType);
            responseInfo.put("resp", respSchema);

            return JsonUtils.toJson(responseInfo);
        } catch (Exception e) {
            log.error("解析响应信息失败: {}", method.getName(), e);
            return "{}";
        }
    }

    /**
     * 解析参数化返回类型
     */
    private Map<String, Object> parseParameterizedReturnType(ParameterizedType parameterizedType) {
        Map<String, Object> responseInfo = new HashMap<>();

        // 解析外层类型（如ResponseBase）的字段
        Class<?> rawType = (Class<?>) parameterizedType.getRawType();
        responseInfo.putAll(parseClassFieldsWithInheritance(rawType));

        // 解析所有泛型参数
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        for (int i = 0; i < actualTypeArguments.length; i++) {
            Type actualType = actualTypeArguments[i];
            Map<String, Object> genericFieldInfo = parseGenericType(actualType);

            if (!genericFieldInfo.isEmpty()) {
                // 通过反射检查外层类的字段，找到对应位置的泛型字段并替换
                replaceGenericFieldsInResponse(responseInfo, rawType, parameterizedType, genericFieldInfo, i);
            }
        }

        return responseInfo;
    }

    /**
     * 获取方法响应描述
     */
    private String getMethodResponseDescription(Method method) {
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            String notes = apiOperation.notes();
            if (StringUtils.isNotBlank(notes)) {
                return notes;
            }
            String value = apiOperation.value();
            if (StringUtils.isNotBlank(value)) {
                return value + "的返回结果";
            }
        }
        return "返回结果";
    }

    /**
     * 创建参数Schema
     */
    private Map<String, Object> createParameterSchema(Parameter parameter, String in) {
        Map<String, Object> schema = new HashMap<>();

        // 获取类型信息
        String type = getJsonSchemaType(parameter.getType());
        schema.put("type", type);

        // 参数位置
        schema.put("in", in);

        // 是否必须
        boolean required = isParameterRequired(parameter);
        schema.put("required", required);

        // 参数说明
        String description = getParameterDescription(parameter);
        schema.put("description", description);

        // 如果是枚举类型，添加enum属性
        if ("string".equals(type) && parameter.getType().isEnum()) {
            List<String> enumValues = getEnumValues(parameter.getType());
            if (!enumValues.isEmpty()) {
                schema.put("enums", enumValues);
            }
        }
        // 如果是对象类型，添加properties
        else if ("object".equals(type)) {
            Map<String, Object> properties = createPropertiesFromClass(parameter.getType());
            if (!properties.isEmpty()) {
                schema.put("properties", properties);
            }
        }
        // 如果是数组类型，添加items
        else if ("array".equals(type)) {
            Map<String, Object> items = createArrayItemsSchema(parameter.getType(), parameter.getParameterizedType());
            if (!items.isEmpty()) {
                schema.put("items", items);
            }
        }

        return schema;
    }

    /**
     * 创建类型Schema（通用方法）
     */
    private Map<String, Object> createTypeSchema(Class<?> clazz, Type genericType) {
        Map<String, Object> schema = new HashMap<>();

        try {
            // 处理泛型类型
            if (genericType instanceof ParameterizedType) {
                ParameterizedType paramType = (ParameterizedType) genericType;
                Class<?> rawType = (Class<?>) paramType.getRawType();

                String type = getJsonSchemaType(rawType);
                schema.put("type", type);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(type) && rawType.isEnum()) {
                    List<String> enumValues = getEnumValues(rawType);
                    if (!enumValues.isEmpty()) {
                        schema.put("enums", enumValues);
                    }
                }
                else if ("object".equals(type)) {
                    // 添加对象描述
                    String description = getClassDescription(rawType);
                    if (StringUtils.isNotBlank(description)) {
                        schema.put("description", description);
                    }

                    // 解析对象的properties
                    Map<String, Object> properties = createPropertiesFromClass(rawType);

                    // 处理泛型字段
                    Type[] typeArgs = paramType.getActualTypeArguments();
                    for (int i = 0; i < typeArgs.length; i++) {
                        replaceGenericFieldsInProperties(properties, rawType, paramType, typeArgs[i], i);
                    }

                    if (!properties.isEmpty()) {
                        schema.put("properties", properties);
                    }
                } else if ("array".equals(type)) {
                    Map<String, Object> items = createArrayItemsSchema(rawType, genericType);
                    if (!items.isEmpty()) {
                        schema.put("items", items);
                    }
                }
            } else {
                // 非泛型类型
                String type = getJsonSchemaType(clazz);
                schema.put("type", type);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(type) && clazz.isEnum()) {
                    List<String> enumValues = getEnumValues(clazz);
                    if (!enumValues.isEmpty()) {
                        schema.put("enums", enumValues);
                    }
                }
                else if ("object".equals(type)) {
                    // 添加对象描述
                    String description = getClassDescription(clazz);
                    if (StringUtils.isNotBlank(description)) {
                        schema.put("description", description);
                    }

                    Map<String, Object> properties = createPropertiesFromClass(clazz);
                    if (!properties.isEmpty()) {
                        schema.put("properties", properties);
                    }
                } else if ("array".equals(type)) {
                    Map<String, Object> items = createArrayItemsSchema(clazz, genericType);
                    if (!items.isEmpty()) {
                        schema.put("items", items);
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建类型Schema失败: {}", clazz.getName(), e);
            // 降级处理
            schema.put("type", getJsonSchemaType(clazz));
        }

        return schema;
    }

    /**
     * 从类创建properties
     */
    private Map<String, Object> createPropertiesFromClass(Class<?> clazz) {
        return createPropertiesFromClass(clazz, new HashSet<>());
    }

    /**
     * 从类创建properties（带循环引用检测）
     */
    private Map<String, Object> createPropertiesFromClass(Class<?> clazz, Set<Class<?>> visitedClasses) {
        Map<String, Object> properties = new HashMap<>();

        if (isBasicType(clazz) || visitedClasses.contains(clazz)) {
            return properties;
        }

        // 添加到已访问类集合中，防止循环引用
        visitedClasses.add(clazz);

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            properties.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldSchema = createFieldSchema(field, visitedClasses);
                    properties.put(field.getName(), fieldSchema);
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("创建properties失败: {}", clazz.getName(), e);
        } finally {
            // 从已访问类集合中移除，允许在其他分支中重新访问
            visitedClasses.remove(clazz);
        }

        return properties;
    }

    /**
     * 创建字段Schema
     */
    private Map<String, Object> createFieldSchema(Field field) {
        return createFieldSchema(field, new HashSet<>());
    }

    /**
     * 创建字段Schema（带循环引用检测）
     */
    private Map<String, Object> createFieldSchema(Field field, Set<Class<?>> visitedClasses) {
        Map<String, Object> schema = new HashMap<>();

        String type = getJsonSchemaType(field.getType());
        schema.put("type", type);

        // 是否必须
        boolean required = isFieldRequired(field);
        schema.put("required", required);

        // 字段说明
        String description = getFieldDescription(field);
        if (StringUtils.isNotBlank(description)) {
            schema.put("description", description);
        }

        // 如果是枚举类型，添加enum属性
        if ("string".equals(type) && field.getType().isEnum()) {
            List<String> enumValues = getEnumValues(field.getType());
            if (!enumValues.isEmpty()) {
                schema.put("enums", enumValues);
            }
        }
        // 如果是对象类型，递归添加properties
        else if ("object".equals(type)) {
            // 检查是否已经访问过此类，防止循环引用
            if (!visitedClasses.contains(field.getType())) {
                // 如果字段描述为空，尝试使用类的ApiModel描述
                if (!schema.containsKey("description")) {
                    String classDescription = getClassDescription(field.getType());
                    if (StringUtils.isNotBlank(classDescription)) {
                        schema.put("description", classDescription);
                    }
                }

                Map<String, Object> properties = createPropertiesFromClass(field.getType(), visitedClasses);
                if (!properties.isEmpty()) {
                    schema.put("properties", properties);
                }
            } else {
                // 循环引用时，只保留基本信息，不展开properties
                schema.put("description", "循环引用: " + field.getType().getSimpleName());
            }
        }
        // 如果是数组类型，添加items
        else if ("array".equals(type)) {
            Map<String, Object> items = createArrayItemsSchema(field.getType(), field.getGenericType(), visitedClasses);
            if (!items.isEmpty()) {
                schema.put("items", items);
            }
        }

        return schema;
    }

    /**
     * 创建数组items的Schema
     */
    private Map<String, Object> createArrayItemsSchema(Class<?> arrayClass, Type genericType) {
        return createArrayItemsSchema(arrayClass, genericType, new HashSet<>());
    }

    /**
     * 创建数组items的Schema（带循环引用检测）
     */
    private Map<String, Object> createArrayItemsSchema(Class<?> arrayClass, Type genericType, Set<Class<?>> visitedClasses) {
        Map<String, Object> items = new HashMap<>();

        try {
            // 处理泛型数组，如List<String>、List<SomeClass>等
            if (genericType instanceof ParameterizedType) {
                ParameterizedType paramType = (ParameterizedType) genericType;
                Type[] typeArgs = paramType.getActualTypeArguments();
                if (typeArgs.length > 0) {
                    Type itemType = typeArgs[0];
                    if (itemType instanceof Class) {
                        Class<?> itemClass = (Class<?>) itemType;
                        String itemSchemaType = getJsonSchemaType(itemClass);
                        items.put("type", itemSchemaType);

                        // 如果是枚举类型，添加enum属性
                        if ("string".equals(itemSchemaType) && itemClass.isEnum()) {
                            List<String> enumValues = getEnumValues(itemClass);
                            if (!enumValues.isEmpty()) {
                                items.put("enums", enumValues);
                            }
                        }
                        else if ("object".equals(itemSchemaType)) {
                            // 检查是否已经访问过此类，防止循环引用
                            if (!visitedClasses.contains(itemClass)) {
                                // 添加对象描述
                                String description = getClassDescription(itemClass);
                                if (StringUtils.isNotBlank(description)) {
                                    items.put("description", description);
                                }

                                Map<String, Object> properties = createPropertiesFromClass(itemClass, visitedClasses);
                                if (!properties.isEmpty()) {
                                    items.put("properties", properties);
                                }
                            } else {
                                // 循环引用时，只保留基本信息
                                items.put("description", "循环引用: " + itemClass.getSimpleName());
                            }
                        }
                    } else if (itemType instanceof ParameterizedType) {
                        // 处理嵌套的泛型类型，如List<ResponseBase<DemoResp>>中的ResponseBase<DemoResp>
                        ParameterizedType itemParamType = (ParameterizedType) itemType;
                        Class<?> itemRawType = (Class<?>) itemParamType.getRawType();
                        String itemSchemaType = getJsonSchemaType(itemRawType);
                        items.put("type", itemSchemaType);

                        if ("object".equals(itemSchemaType)) {
                            // 递归调用createTypeSchema来处理复杂的泛型类型
                            Map<String, Object> itemSchema = createTypeSchema(itemRawType, itemType);
                            // 将itemSchema的内容合并到items中，但排除type字段（已经设置过了）
                            for (Map.Entry<String, Object> entry : itemSchema.entrySet()) {
                                if (!"type".equals(entry.getKey())) {
                                    items.put(entry.getKey(), entry.getValue());
                                }
                            }
                        }
                    }
                }
            }
            // 处理数组类型，如String[]、SomeClass[]等
            else if (arrayClass.isArray()) {
                Class<?> componentType = arrayClass.getComponentType();
                String itemSchemaType = getJsonSchemaType(componentType);
                items.put("type", itemSchemaType);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(itemSchemaType) && componentType.isEnum()) {
                    List<String> enumValues = getEnumValues(componentType);
                    if (!enumValues.isEmpty()) {
                        items.put("enums", enumValues);
                    }
                }
                else if ("object".equals(itemSchemaType)) {
                    // 检查是否已经访问过此类，防止循环引用
                    if (!visitedClasses.contains(componentType)) {
                        // 添加对象描述
                        String description = getClassDescription(componentType);
                        if (StringUtils.isNotBlank(description)) {
                            items.put("description", description);
                        }

                        Map<String, Object> properties = createPropertiesFromClass(componentType, visitedClasses);
                        if (!properties.isEmpty()) {
                            items.put("properties", properties);
                        }
                    } else {
                        // 循环引用时，只保留基本信息
                        items.put("description", "循环引用: " + componentType.getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建数组items schema失败", e);
        }

        return items;
    }

    /**
     * 解析类的字段信息（包括父类字段）
     */
    private Map<String, Object> parseClassFieldsWithInheritance(Class<?> clazz) {
        Map<String, Object> fieldsInfo = new HashMap<>();

        if (isBasicType(clazz)) {
            return fieldsInfo;
        }

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            fieldsInfo.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldInfo = new HashMap<>();
                    fieldInfo.put("name", field.getName());
                    fieldInfo.put("type", getSimpleTypeName(field.getType()));
                    fieldInfo.put("required", isFieldRequired(field));
                    fieldInfo.put("description", getFieldDescription(field));
                    fieldsInfo.put(field.getName(), fieldInfo);
                }

                // 移动到父类
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("解析类字段失败: {}", clazz.getName(), e);
        }

        return fieldsInfo;
    }

    /**
     * 判断参数是否必须
     */
    private boolean isParameterRequired(Parameter parameter) {
        // 检查@NotNull、@NotBlank等验证注解
        if (parameter.isAnnotationPresent(NotNull.class) ||
                parameter.isAnnotationPresent(NotBlank.class) ||
                parameter.isAnnotationPresent(NotEmpty.class)) {
            return true;
        }

        // 检查Spring注解的required属性
        if (parameter.isAnnotationPresent(RequestParam.class)) {
            return parameter.getAnnotation(RequestParam.class).required();
        }
        if (parameter.isAnnotationPresent(RequestHeader.class)) {
            return parameter.getAnnotation(RequestHeader.class).required();
        }
        if (parameter.isAnnotationPresent(PathVariable.class)) {
            return parameter.getAnnotation(PathVariable.class).required();
        }

        return false;
    }

    /**
     * 判断字段是否必须
     */
    private boolean isFieldRequired(Field field) {
        return field.isAnnotationPresent(NotNull.class) ||
                field.isAnnotationPresent(NotBlank.class) ||
                field.isAnnotationPresent(NotEmpty.class);
    }

    /**
     * 获取参数描述
     */
    private String getParameterDescription(Parameter parameter) {
        if (parameter.isAnnotationPresent(ApiParam.class)) {
            return parameter.getAnnotation(ApiParam.class).value();
        }
        return "";
    }

    /**
     * 获取字段描述
     */
    private String getFieldDescription(Field field) {
        if (field.isAnnotationPresent(ApiModelProperty.class)) {
            return field.getAnnotation(ApiModelProperty.class).value();
        }
        return "";
    }

    /**
     * 获取类描述（从ApiModel注解）
     */
    private String getClassDescription(Class<?> clazz) {
        if (clazz.isAnnotationPresent(ApiModel.class)) {
            ApiModel apiModel = clazz.getAnnotation(ApiModel.class);
            String value = apiModel.value();
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            String description = apiModel.description();
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        return "";
    }

    /**
     * 解析接口名称信息
     */
    private String parseApiName(Method method) {
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);

            // 优先使用value属性
            String value = apiOperation.value();
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }

//            // 其次使用notes属性
//            String notes = apiOperation.notes();
//            if (StringUtils.isNotEmpty(notes)) {
//                return notes;
//            }
        }

        return "";
    }

    /**
     * 获取枚举的所有值
     */
    private List<String> getEnumValues(Class<?> enumClass) {
        List<String> enumValues = new ArrayList<>();
        if (enumClass.isEnum()) {
            try {
                Object[] enumConstants = enumClass.getEnumConstants();
                for (Object enumConstant : enumConstants) {
                    enumValues.add(((Enum<?>) enumConstant).name());
                }
            } catch (Exception e) {
                log.error("获取枚举值失败: {}", enumClass.getName(), e);
            }
        }
        return enumValues;
    }

    /**
     * 判断是否为基本类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Integer.class || clazz == int.class ||
                clazz == Long.class || clazz == long.class ||
                clazz == Double.class || clazz == double.class ||
                clazz == Float.class || clazz == float.class ||
                clazz == Boolean.class || clazz == boolean.class ||
                clazz == Byte.class || clazz == byte.class ||
                clazz == Short.class || clazz == short.class ||
                clazz == Character.class || clazz == char.class ||
                clazz == Void.class || clazz == void.class ||
                Number.class.isAssignableFrom(clazz);
    }

    /**
     * 获取简单类型名称
     */
    private String getSimpleTypeName(Class<?> clazz) {
        if (clazz == int.class || clazz == Integer.class) return "integer";
        if (clazz == long.class || clazz == Long.class) return "long";
        if (clazz == double.class || clazz == Double.class) return "double";
        if (clazz == float.class || clazz == Float.class) return "float";
        if (clazz == boolean.class || clazz == Boolean.class) return "boolean";
        if (clazz == String.class) return "string";
        return clazz.getSimpleName();
    }

    /**
     * 创建基本类型信息
     */
    private Map<String, Object> createBasicTypeInfo(Class<?> clazz, String description) {
        Map<String, Object> info = new HashMap<>();
        info.put("type", getSimpleTypeName(clazz));
        info.put("description", description);
        info.put("required", false);

        return info;
    }

    /**
     * 解析泛型类型
     */
    private Map<String, Object> parseGenericType(Type type) {
        Map<String, Object> typeInfo = new HashMap<>();

        try {
            if (type instanceof Class) {
                Class<?> clazz = (Class<?>) type;
                if (!isBasicType(clazz) && !clazz.equals(Void.class)) {
                    typeInfo = parseClassFieldsWithInheritance(clazz);
                } else if (!clazz.equals(Void.class)) {
                    typeInfo = createBasicTypeInfo(clazz, "返回数据");
                }
            } else if (type instanceof ParameterizedType) {
                // 处理嵌套泛型，如List<SomeClass>、PageDTO<SomeClass>等
                ParameterizedType nestedType = (ParameterizedType) type;
                Type[] nestedArgs = nestedType.getActualTypeArguments();

                // 解析外层泛型类型（如List、PageDTO等）
                Class<?> outerClass = (Class<?>) nestedType.getRawType();
                typeInfo = parseClassFieldsWithInheritance(outerClass);

                // 处理所有嵌套的泛型参数
                for (int i = 0; i < nestedArgs.length; i++) {
                    Map<String, Object> nestedTypeInfo = parseGenericType(nestedArgs[i]);
                    if (!nestedTypeInfo.isEmpty()) {
                        // 通过反射检查外层类的字段，找到对应的泛型字段并替换
                        replaceGenericFieldsInType(typeInfo, outerClass, nestedType, nestedTypeInfo, i);
                    }
                }
            } else if (type instanceof TypeVariable) {
                // 处理类型变量，创建占位符信息
                TypeVariable<?> typeVar = (TypeVariable<?>) type;
                typeInfo.put("typeVariable", typeVar.getName());
                typeInfo.put("description", "泛型类型: " + typeVar.getName());
            }
        } catch (Exception e) {
            log.error("解析泛型类型失败: {}", type, e);
        }

        return typeInfo;
    }

    /**
     * 通过反射检查并替换响应中的泛型字段
     */
    private void replaceGenericFieldsInResponse(Map<String, Object> responseInfo, Class<?> rawType,
                                                ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (responseInfo.containsKey(fieldName)) {
                                    responseInfo.put(fieldName, genericFieldInfo);
                                    return; // 找到匹配的字段后返回
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量（如 T、E 等）
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (responseInfo.containsKey(fieldName)) {
                                responseInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换泛型字段失败", e);
        }
    }

    /**
     * 检查泛型类型是否匹配
     */
    private boolean isGenericTypeMatch(Type fieldType, Type methodType) {
        if (fieldType instanceof TypeVariable && methodType instanceof TypeVariable) {
            return ((TypeVariable<?>) fieldType).getName().equals(((TypeVariable<?>) methodType).getName());
        }
        if (fieldType instanceof Class && methodType instanceof Class) {
            return fieldType.equals(methodType);
        }
        if (fieldType instanceof TypeVariable || methodType instanceof TypeVariable) {
            return true; // 类型变量可以匹配任何类型
        }
        return fieldType.equals(methodType);
    }

    /**
     * 检查类型变量是否匹配（支持类型参数索引）
     */
    private boolean isTypeVariableMatch(TypeVariable<?> typeVar, Class<?> declaringClass, Type actualType, int typeArgIndex) {
        // 获取类的泛型参数声明
        TypeVariable<?>[] classTypeParams = declaringClass.getTypeParameters();
        for (int i = 0; i < classTypeParams.length; i++) {
            if (classTypeParams[i].getName().equals(typeVar.getName())) {
                // 检查类型变量的位置是否与期望的索引匹配
                return i == typeArgIndex;
            }
        }
        return false;
    }

    /**
     * 通过反射检查并替换类型中的泛型字段（用于嵌套泛型处理）
     */
    private void replaceGenericFieldsInType(Map<String, Object> typeInfo, Class<?> outerClass,
                                            ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = outerClass;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (typeInfo.containsKey(fieldName)) {
                                    typeInfo.put(fieldName, genericFieldInfo);
                                    return;
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, outerClass, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (typeInfo.containsKey(fieldName)) {
                                typeInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换嵌套泛型字段失败", e);
        }
    }

    /**
     * 获取JSON Schema类型
     */
    private String getJsonSchemaType(Class<?> clazz) {
        // 基本数据类型
        if (clazz == int.class || clazz == Integer.class ||
            clazz == long.class || clazz == Long.class ||
            clazz == short.class || clazz == Short.class ||
            clazz == byte.class || clazz == Byte.class) {
            return "integer";
        }
        if (clazz == double.class || clazz == Double.class ||
            clazz == float.class || clazz == Float.class) {
            return "number";
        }
        if (clazz == boolean.class || clazz == Boolean.class) {
            return "boolean";
        }
        if (clazz == String.class || clazz == char.class || clazz == Character.class) {
            return "string";
        }

        // 枚举类型作为string处理
        if (clazz.isEnum()) {
            return "string";
        }

        // 数组和集合类型
        if (clazz.isArray() ||
            java.util.Collection.class.isAssignableFrom(clazz) ||
            java.util.List.class.isAssignableFrom(clazz) ||
            java.util.Set.class.isAssignableFrom(clazz)) {
            return "array";
        }

        // 其他复杂对象
        return "object";
    }

    /**
     * 在properties中替换泛型字段
     */
    private void replaceGenericFieldsInProperties(Map<String, Object> properties, Class<?> rawType,
                                                 ParameterizedType paramType, Type actualType, int typeArgIndex) {
        try {
            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    String fieldName = field.getName();
                    if (!properties.containsKey(fieldName)) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof TypeVariable) {
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, actualType, typeArgIndex)) {
                            // 替换泛型字段的schema
                            Map<String, Object> fieldSchema = createTypeSchema(
                                actualType instanceof Class ? (Class<?>) actualType : Object.class,
                                actualType
                            );
                            // 保留原有的required和description信息
                            Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                            if (originalSchema.containsKey("required")) {
                                fieldSchema.put("required", originalSchema.get("required"));
                            }
                            if (originalSchema.containsKey("description")) {
                                fieldSchema.put("description", originalSchema.get("description"));
                            }
                            properties.put(fieldName, fieldSchema);
                        }
                    } else if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, actualType)) {
                                // 替换泛型字段的schema
                                Map<String, Object> fieldSchema = createTypeSchema(
                                    (Class<?>) fieldParamType.getRawType(),
                                    fieldGenericType
                                );
                                // 保留原有的required和description信息
                                Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                                if (originalSchema.containsKey("required")) {
                                    fieldSchema.put("required", originalSchema.get("required"));
                                }
                                if (originalSchema.containsKey("description")) {
                                    fieldSchema.put("description", originalSchema.get("description"));
                                }
                                properties.put(fieldName, fieldSchema);
                                break;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换properties中的泛型字段失败", e);
        }
    }

    /**
     * 初始化当前项目的包名前缀（基于主应用类的包）
     */
    private void initProjectPackagePres() {
        try {
            // 查找带有@SpringBootApplication注解的主类（项目入口类）
            Map<String, Object> bootBeans = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
            if (!bootBeans.isEmpty()) {
                // 取第一个主类（通常一个项目只有一个）
                Class<?> mainClass = bootBeans.values().iterator().next().getClass();
                // 获取主类的包名（即项目根包）
                String mainPackage = mainClass.getPackage().getName();
                projectPackagePres.add(mainPackage);
            } else {
                // 未找到主类时，fallback到原有默认前缀
                log.warn("未找到主应用类，使用默认包名前缀");
            }
        } catch (Exception e) {
            // 异常时fallback到默认前缀
            log.error("获取项目包名前缀失败，使用默认值", e);
        }
    }
}
